import 'package:intl/intl.dart';

class CurrencyFormatter {
  static final NumberFormat _formatter = NumberFormat.currency(
    locale: 'fa_AF', // Persian (Afghanistan)
    symbol: 'افغانی',
    decimalDigits: 0,
  );

  static final NumberFormat _compactFormatter = NumberFormat.compact(
    locale: 'fa_AF',
  );

  /// Format amount as Afghan currency
  static String formatAFN(double amount) {
    return _formatter.format(amount);
  }

  /// Format amount as compact Afghan currency (e.g., 1.2K افغانی)
  static String formatCompactAFN(double amount) {
    return '${_compactFormatter.format(amount)} افغانی';
  }

  /// Format amount with Persian digits
  static String formatWithPersianDigits(double amount) {
    final formatted = NumberFormat('#,##0', 'fa_AF').format(amount);
    return '$formatted افغانی';
  }

  /// Convert English digits to Persian digits
  static String toPersianDigits(String input) {
    const englishDigits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    const persianDigits = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
    
    String result = input;
    for (int i = 0; i < englishDigits.length; i++) {
      result = result.replaceAll(englishDigits[i], persianDigits[i]);
    }
    return result;
  }

  /// Parse Persian/English digits to double
  static double parseAmount(String input) {
    // Convert Persian digits to English
    String normalized = input;
    const persianDigits = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
    const englishDigits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    
    for (int i = 0; i < persianDigits.length; i++) {
      normalized = normalized.replaceAll(persianDigits[i], englishDigits[i]);
    }
    
    // Remove currency symbols and commas
    normalized = normalized.replaceAll(RegExp(r'[^\d.]'), '');
    
    return double.tryParse(normalized) ?? 0.0;
  }
}

class Loan {
  late String id;
  late String name;
  late String type; // "بدهی" or "طلب"
  late String person;
  late double initialAmount;
  late DateTime startDate;
  late String status; // "فعال" or "تمام شده"
  late String userId;
  late DateTime createdAt;
  late DateTime updatedAt;

  Loan();

  Loan.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    name = json['name'];
    type = json['type'];
    person = json['person'];
    initialAmount = (json['initial_amount'] as num).toDouble();
    startDate = DateTime.parse(json['start_date']);
    status = json['status'];
    createdAt = DateTime.parse(json['created_at']);
    updatedAt = DateTime.parse(json['updated_at']);
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'name': name,
      'type': type,
      'person': person,
      'initial_amount': initialAmount,
      'start_date': startDate.toIso8601String().split('T')[0], // Date only
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

class Budget {
  late String id;
  late DateTime period;
  late double amount;
  late String categoryId;
  late String userId;
  late DateTime createdAt;
  late DateTime updatedAt;

  Budget();

  Budget.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    categoryId = json['category_id'];
    period = DateTime.parse(json['period']);
    amount = (json['amount'] as num).toDouble();
    createdAt = DateTime.parse(json['created_at']);
    updatedAt = DateTime.parse(json['updated_at']);
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'category_id': categoryId,
      'period': period.toIso8601String().split('T')[0], // Date only
      'amount': amount,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import '../config/supabase_config.dart';

/// Singleton service to manage Supabase client and connectivity
class SupabaseService {
  static SupabaseService? _instance;
  static SupabaseService get instance => _instance ??= SupabaseService._();
  
  SupabaseService._();
  
  SupabaseClient? _client;
  bool _isInitialized = false;
  bool _isOnline = true;
  
  /// Get the Supabase client instance
  SupabaseClient get client {
    if (!_isInitialized) {
      throw Exception('SupabaseService not initialized. Call initialize() first.');
    }
    return _client!;
  }
  
  /// Check if the service is initialized
  bool get isInitialized => _isInitialized;
  
  /// Check if the device is online
  bool get isOnline => _isOnline;
  
  /// Initialize Supabase client
  Future<void> initialize() async {
    try {
      if (_isInitialized) return;
      
      await Supabase.initialize(
        url: SupabaseConfig.supabaseUrl,
        anonKey: SupabaseConfig.supabaseAnonKey,
        debug: kDebugMode,
      );
      
      _client = Supabase.instance.client;
      _isInitialized = true;
      
      // Start monitoring connectivity
      _startConnectivityMonitoring();
      
      debugPrint('SupabaseService initialized successfully');
    } catch (e) {
      debugPrint('Failed to initialize SupabaseService: $e');
      rethrow;
    }
  }
  
  /// Start monitoring network connectivity
  void _startConnectivityMonitoring() {
    Connectivity().onConnectivityChanged.listen((List<ConnectivityResult> results) {
      _isOnline = results.any((result) => 
        result == ConnectivityResult.mobile || 
        result == ConnectivityResult.wifi ||
        result == ConnectivityResult.ethernet
      );
      debugPrint('Connectivity changed: ${_isOnline ? 'Online' : 'Offline'}');
    });
  }
  
  /// Check current connectivity status
  Future<bool> checkConnectivity() async {
    try {
      final results = await Connectivity().checkConnectivity();
      _isOnline = results.any((result) => 
        result == ConnectivityResult.mobile || 
        result == ConnectivityResult.wifi ||
        result == ConnectivityResult.ethernet
      );
      return _isOnline;
    } catch (e) {
      debugPrint('Error checking connectivity: $e');
      _isOnline = false;
      return false;
    }
  }
  
  /// Execute a function with retry logic for network operations
  Future<T> executeWithRetry<T>(
    Future<T> Function() operation, {
    int maxRetries = SupabaseConfig.maxRetryAttempts,
    Duration delay = const Duration(seconds: 1),
  }) async {
    int attempts = 0;
    
    while (attempts < maxRetries) {
      try {
        if (!await checkConnectivity()) {
          throw Exception('No internet connection');
        }
        
        return await operation();
      } catch (e) {
        attempts++;
        if (attempts >= maxRetries) {
          rethrow;
        }
        
        debugPrint('Operation failed (attempt $attempts/$maxRetries): $e');
        await Future.delayed(delay * attempts); // Exponential backoff
      }
    }
    
    throw Exception('Max retry attempts reached');
  }
  
  /// Dispose resources
  void dispose() {
    _client = null;
    _isInitialized = false;
  }
}

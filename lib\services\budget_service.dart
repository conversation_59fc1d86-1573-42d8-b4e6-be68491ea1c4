import 'package:flutter/foundation.dart';
import 'package:my_fincance_app/models/budget.dart';
import 'package:my_fincance_app/models/transaction.dart';
import 'package:my_fincance_app/core/services/supabase_service.dart';
import 'package:uuid/uuid.dart';

class BudgetService extends ChangeNotifier {
  final String _userId;
  final _uuid = Uuid();

  List<Budget> _budgets = [];
  List<Transaction> _transactions = [];
  bool _isLoading = false;

  BudgetService(this._userId) {
    if (_userId.isNotEmpty) {
      _loadBudgets();
    }
  }

  List<Budget> get budgets => _budgets;
  bool get isLoading => _isLoading;

  /// Get budgets for current month
  List<Budget> get currentMonthBudgets {
    final now = DateTime.now();
    return _budgets
        .where((b) => b.period.year == now.year && b.period.month == now.month)
        .toList();
  }

  Future<void> _loadBudgets() async {
    if (_userId.isEmpty) return;

    _isLoading = true;
    notifyListeners();

    try {
      // Load budgets
      final budgetsResponse = await SupabaseService.instance.client
          .from('budgets')
          .select()
          .eq('user_id', _userId)
          .eq('is_deleted', false)
          .order('period', ascending: false);

      _budgets = (budgetsResponse as List)
          .map((json) => Budget.fromJson(json))
          .toList();

      // Load transactions for budget calculations
      final transactionsResponse = await SupabaseService.instance.client
          .from('transactions')
          .select()
          .eq('user_id', _userId)
          .eq('is_deleted', false)
          .eq('type', 'مصرف');

      _transactions = (transactionsResponse as List)
          .map((json) => Transaction.fromJson(json))
          .toList();
    } catch (e) {
      debugPrint('Error loading budgets: $e');
      _budgets = [];
      _transactions = [];
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Get actual spending for a category in a specific month
  double getActualSpending(String categoryId, DateTime period) {
    final transactions = _transactions
        .where(
          (t) =>
              t.categoryId == categoryId &&
              t.type == 'مصرف' &&
              t.date.year == period.year &&
              t.date.month == period.month,
        )
        .toList();

    return transactions.fold<double>(0.0, (sum, t) => sum + t.amount);
  }

  /// Get budget progress (0.0 to 1.0+) for a specific budget
  double getBudgetProgress(Budget budget) {
    final actualSpending = getActualSpending(budget.categoryId, budget.period);
    return budget.amount > 0 ? actualSpending / budget.amount : 0.0;
  }

  /// Check if budget is exceeded
  bool isBudgetExceeded(Budget budget) {
    return getBudgetProgress(budget) > 1.0;
  }

  /// Get budget vs actual data for current month
  Map<String, Map<String, double>> getCurrentMonthBudgetVsActual() {
    final result = <String, Map<String, double>>{};

    for (final budget in currentMonthBudgets) {
      final actualSpending = getActualSpending(
        budget.categoryId,
        budget.period,
      );
      result[budget.categoryId] = {
        'budget': budget.amount,
        'actual': actualSpending,
        'remaining': budget.amount - actualSpending,
      };
    }

    return result;
  }

  Future<void> addBudget(
    DateTime period,
    double amount,
    String categoryId,
  ) async {
    if (_userId.isEmpty) return;

    try {
      final newBudget = Budget()
        ..id = _uuid.v4()
        ..period = period
        ..amount = amount
        ..categoryId = categoryId
        ..userId = _userId
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now();

      await SupabaseService.instance.client
          .from('budgets')
          .insert(newBudget.toJson());

      await _loadBudgets();
    } catch (e) {
      debugPrint('Error adding budget: $e');
      rethrow;
    }
  }

  Future<void> updateBudget(
    Budget budget,
    DateTime newPeriod,
    double newAmount,
  ) async {
    try {
      budget.period = newPeriod;
      budget.amount = newAmount;
      budget.updatedAt = DateTime.now();

      await SupabaseService.instance.client
          .from('budgets')
          .update(budget.toJson())
          .eq('id', budget.id);

      await _loadBudgets();
    } catch (e) {
      debugPrint('Error updating budget: $e');
      rethrow;
    }
  }

  Future<void> deleteBudget(Budget budget) async {
    try {
      // Soft delete
      await SupabaseService.instance.client
          .from('budgets')
          .update({
            'is_deleted': true,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', budget.id);

      await _loadBudgets();
    } catch (e) {
      debugPrint('Error deleting budget: $e');
      rethrow;
    }
  }

  /// Refresh budget data (for pull-to-refresh functionality)
  Future<void> refresh() async {
    await _loadBudgets();
  }

  /// Get budget summary for a specific period
  Map<String, double> getBudgetSummaryForPeriod(DateTime period) {
    final periodBudgets = _budgets
        .where(
          (b) => b.period.year == period.year && b.period.month == period.month,
        )
        .toList();

    double totalBudget = 0.0;
    double totalSpent = 0.0;

    for (final budget in periodBudgets) {
      totalBudget += budget.amount;
      totalSpent += getActualSpending(budget.categoryId, budget.period);
    }

    return {
      'totalBudget': totalBudget,
      'totalSpent': totalSpent,
      'remaining': totalBudget - totalSpent,
    };
  }

  /// Get all budget periods (for historical analysis)
  List<DateTime> get allBudgetPeriods {
    final periods = _budgets
        .map((b) => DateTime(b.period.year, b.period.month))
        .toSet()
        .toList();
    periods.sort((a, b) => b.compareTo(a)); // Most recent first
    return periods;
  }
}

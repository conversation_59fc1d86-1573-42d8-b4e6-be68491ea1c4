import 'package:flutter/foundation.dart';
import 'package:my_fincance_app/models/loan.dart';
import 'package:my_fincance_app/models/transaction.dart';
import 'package:my_fincance_app/core/services/supabase_service.dart';
import 'package:uuid/uuid.dart';

class LoanService extends ChangeNotifier {
  final String _userId;
  final _uuid = Uuid();

  List<Loan> _loans = [];
  List<Transaction> _transactions = [];
  bool _isLoading = false;

  LoanService(this._userId) {
    if (_userId.isNotEmpty) {
      _loadLoans();
    }
  }

  List<Loan> get loans => _loans;
  List<Loan> get debtLoans => _loans.where((l) => l.type == 'بدهی').toList();
  List<Loan> get creditLoans => _loans.where((l) => l.type == 'طلب').toList();
  List<Loan> get activeLoans =>
      _loans.where((l) => l.status == 'فعال').toList();
  bool get isLoading => _isLoading;

  Future<void> _loadLoans() async {
    if (_userId.isEmpty) return;

    _isLoading = true;
    notifyListeners();

    try {
      // Load loans
      final loansResponse = await SupabaseService.instance.client
          .from('loans')
          .select()
          .eq('user_id', _userId)
          .eq('is_deleted', false)
          .order('created_at', ascending: false);

      _loans = (loansResponse as List)
          .map((json) => Loan.fromJson(json))
          .toList();

      // Load transactions for loan calculations
      final transactionsResponse = await SupabaseService.instance.client
          .from('transactions')
          .select()
          .eq('user_id', _userId)
          .eq('is_deleted', false)
          .not('loan_id', 'is', null);

      _transactions = (transactionsResponse as List)
          .map((json) => Transaction.fromJson(json))
          .toList();

      _updateLoanStatuses();
    } catch (e) {
      debugPrint('Error loading loans: $e');
      _loans = [];
      _transactions = [];
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Calculate remaining balance for a specific loan
  double getRemainingBalance(String loanId) {
    final loanTransactions = _transactions
        .where((t) => t.loanId == loanId)
        .toList();

    final totalPaid = loanTransactions.fold<double>(
      0.0,
      (sum, transaction) => sum + transaction.amount,
    );
    final loan = _loans.firstWhere((l) => l.id == loanId);
    return loan.initialAmount - totalPaid;
  }

  /// Get total active debt amount
  double getTotalActiveDebt() {
    return debtLoans
        .where((loan) => loan.status == 'فعال')
        .fold<double>(0.0, (sum, loan) => sum + getRemainingBalance(loan.id));
  }

  /// Get total active credit amount
  double getTotalActiveCredit() {
    return creditLoans
        .where((loan) => loan.status == 'فعال')
        .fold<double>(0.0, (sum, loan) => sum + getRemainingBalance(loan.id));
  }

  /// Update loan statuses based on remaining balances
  Future<void> _updateLoanStatuses() async {
    final loansToUpdate = <Map<String, dynamic>>[];

    for (final loan in _loans) {
      if (loan.status == 'فعال') {
        final remainingBalance = getRemainingBalance(loan.id);
        if (remainingBalance <= 0) {
          loan.status = 'تمام شده';
          loan.updatedAt = DateTime.now();
          loansToUpdate.add({
            'id': loan.id,
            'status': 'تمام شده',
            'updated_at': loan.updatedAt.toIso8601String(),
          });
        }
      }
    }

    // Update all changed loans in batch
    if (loansToUpdate.isNotEmpty) {
      try {
        for (final loanUpdate in loansToUpdate) {
          await SupabaseService.instance.client
              .from('loans')
              .update(loanUpdate)
              .eq('id', loanUpdate['id']);
        }
      } catch (e) {
        debugPrint('Error updating loan statuses: $e');
      }
    }
  }

  /// Get loan repayment progress (0.0 to 1.0)
  double getLoanProgress(String loanId) {
    final loan = _loans.firstWhere((l) => l.id == loanId);
    final remainingBalance = getRemainingBalance(loanId);
    final paidAmount = loan.initialAmount - remainingBalance;
    return loan.initialAmount > 0
        ? (paidAmount / loan.initialAmount).clamp(0.0, 1.0)
        : 0.0;
  }

  Future<void> addLoan(
    String name,
    String type,
    String person,
    double initialAmount,
    DateTime startDate,
  ) async {
    if (_userId.isEmpty) return;

    try {
      final newLoan = Loan()
        ..id = _uuid.v4()
        ..name = name
        ..type = type
        ..person = person
        ..initialAmount = initialAmount
        ..startDate = startDate
        ..status = 'فعال'
        ..userId = _userId
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now();

      await SupabaseService.instance.client
          .from('loans')
          .insert(newLoan.toJson());

      await _loadLoans();
    } catch (e) {
      debugPrint('Error adding loan: $e');
      rethrow;
    }
  }

  /// Force update loan statuses (call this after adding transactions)
  Future<void> updateLoanStatuses() async {
    await _updateLoanStatuses();
    notifyListeners();
  }

  Future<void> updateLoan(
    Loan loan,
    String newName,
    String newType,
    String newPerson,
    double newInitialAmount,
    DateTime newStartDate,
  ) async {
    try {
      loan.name = newName;
      loan.type = newType;
      loan.person = newPerson;
      loan.initialAmount = newInitialAmount;
      loan.startDate = newStartDate;
      loan.updatedAt = DateTime.now();

      await SupabaseService.instance.client
          .from('loans')
          .update(loan.toJson())
          .eq('id', loan.id);

      await _loadLoans();
    } catch (e) {
      debugPrint('Error updating loan: $e');
      rethrow;
    }
  }

  Future<void> deleteLoan(Loan loan) async {
    try {
      // Soft delete
      await SupabaseService.instance.client
          .from('loans')
          .update({
            'is_deleted': true,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', loan.id);

      await _loadLoans();
    } catch (e) {
      debugPrint('Error deleting loan: $e');
      rethrow;
    }
  }

  /// Refresh loan data (for pull-to-refresh functionality)
  Future<void> refresh() async {
    await _loadLoans();
  }
}

import 'package:flutter/foundation.dart' hide Category;
import 'package:my_fincance_app/models/category.dart';
import 'package:my_fincance_app/core/services/supabase_service.dart';
import 'package:uuid/uuid.dart';

class CategoryService extends ChangeNotifier {
  final String _userId;
  final _uuid = Uuid();

  List<Category> _categories = [];
  bool _isLoading = false;

  CategoryService(this._userId) {
    if (_userId.isNotEmpty) {
      _loadCategories();
    }
  }

  List<Category> get categories => _categories;
  bool get isLoading => _isLoading;

  Future<void> _loadCategories() async {
    if (_userId.isEmpty) return;

    _isLoading = true;
    notifyListeners();

    try {
      final response = await SupabaseService.instance.client
          .from('categories')
          .select()
          .eq('user_id', _userId)
          .eq('is_deleted', false)
          .order('name');

      _categories = (response as List)
          .map((json) => Category.fromJson(json))
          .toList();

      // Initialize default categories if none exist
      if (_categories.isEmpty) {
        await _initializeDefaultCategories();
      }
    } catch (e) {
      debugPrint('Error loading categories: $e');
      _categories = [];
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> addCategory(String name, String type) async {
    if (_userId.isEmpty) return;

    try {
      final newCategory = Category()
        ..id = _uuid.v4()
        ..name = name
        ..type = type
        ..userId = _userId
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now();

      await SupabaseService.instance.client
          .from('categories')
          .insert(newCategory.toJson());

      await _loadCategories();
    } catch (e) {
      debugPrint('Error adding category: $e');
      rethrow;
    }
  }

  Future<void> updateCategory(
    Category category,
    String newName,
    String newType,
  ) async {
    try {
      category.name = newName;
      category.type = newType;
      category.updatedAt = DateTime.now();

      await SupabaseService.instance.client
          .from('categories')
          .update(category.toJson())
          .eq('id', category.id);

      await _loadCategories();
    } catch (e) {
      debugPrint('Error updating category: $e');
      rethrow;
    }
  }

  Future<void> deleteCategory(Category category) async {
    try {
      // Soft delete
      await SupabaseService.instance.client
          .from('categories')
          .update({
            'is_deleted': true,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', category.id);

      await _loadCategories();
    } catch (e) {
      debugPrint('Error deleting category: $e');
      rethrow;
    }
  }

  Future<void> _initializeDefaultCategories() async {
    if (_userId.isEmpty) return;

    try {
      // Default income categories
      final defaultIncomeCategories = [
        'معاش',
        'کسب و کار',
        'سرمایه‌گذاری',
        'هدیه',
        'سایر درآمدها',
      ];

      // Default expense categories
      final defaultExpenseCategories = [
        'غذا و نوشیدنی',
        'حمل و نقل',
        'خرید',
        'تفریح',
        'بهداشت و درمان',
        'آموزش',
        'خانه',
        'لباس',
        'سایر مصارف',
      ];

      final categoriesToInsert = <Map<String, dynamic>>[];

      // Add default income categories
      for (final categoryName in defaultIncomeCategories) {
        final category = Category()
          ..id = _uuid.v4()
          ..name = categoryName
          ..type = 'درآمد'
          ..userId = _userId
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now();
        categoriesToInsert.add(category.toJson());
      }

      // Add default expense categories
      for (final categoryName in defaultExpenseCategories) {
        final category = Category()
          ..id = _uuid.v4()
          ..name = categoryName
          ..type = 'مصرف'
          ..userId = _userId
          ..createdAt = DateTime.now()
          ..updatedAt = DateTime.now();
        categoriesToInsert.add(category.toJson());
      }

      // Insert all categories at once
      await SupabaseService.instance.client
          .from('categories')
          .insert(categoriesToInsert);

      await _loadCategories();
    } catch (e) {
      debugPrint('Error initializing default categories: $e');
    }
  }
}

class Transaction {
  late String id;
  late DateTime date;
  late String description;
  late double amount;
  late String type; // "درآمد" or "مصرف"
  late String categoryId;
  String? loanId;
  late String userId;
  late DateTime createdAt;
  late DateTime updatedAt;

  Transaction();

  Transaction.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    categoryId = json['category_id'];
    loanId = json['loan_id'];
    date = DateTime.parse(json['date']);
    description = json['description'];
    amount = (json['amount'] as num).toDouble();
    type = json['type'];
    createdAt = DateTime.parse(json['created_at']);
    updatedAt = DateTime.parse(json['updated_at']);
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'category_id': categoryId,
      'loan_id': loanId,
      'date': date.toIso8601String().split('T')[0], // Date only
      'description': description,
      'amount': amount,
      'type': type,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

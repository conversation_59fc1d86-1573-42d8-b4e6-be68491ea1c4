import 'package:flutter/material.dart';
import 'package:shamsi_date/shamsi_date.dart';

class ShamsiDatePicker {
  /// Show Solar Hijri date picker dialog
  static Future<DateTime?> showShamsiDatePicker({
    required BuildContext context,
    required DateTime initialDate,
    required DateTime firstDate,
    required DateTime lastDate,
    String? helpText,
    String? cancelText,
    String? confirmText,
  }) async {
    final initialJalali = Jalali.fromDateTime(initialDate);
    final firstJalali = Jalali.fromDateTime(firstDate);
    final lastJalali = Jalali.fromDateTime(lastDate);

    return showDialog<DateTime>(
      context: context,
      builder: (BuildContext context) {
        return _ShamsiDatePickerDialog(
          initialDate: initialJalali,
          firstDate: firstJalali,
          lastDate: lastJalali,
          helpText: helpText ?? 'انتخاب تاریخ',
          cancelText: cancelText ?? 'لغو',
          confirmText: confirmText ?? 'تأیید',
        );
      },
    );
  }
}

class _ShamsiDatePickerDialog extends StatefulWidget {
  final Jala<PERSON> initialDate;
  final Jalali firstDate;
  final Jalali lastDate;
  final String helpText;
  final String cancelText;
  final String confirmText;

  const _ShamsiDatePickerDialog({
    required this.initialDate,
    required this.firstDate,
    required this.lastDate,
    required this.helpText,
    required this.cancelText,
    required this.confirmText,
  });

  @override
  State<_ShamsiDatePickerDialog> createState() =>
      _ShamsiDatePickerDialogState();
}

class _ShamsiDatePickerDialogState extends State<_ShamsiDatePickerDialog> {
  late Jalali selectedDate;
  late PageController _pageController;
  late int currentMonthIndex;

  // Persian month names
  static const List<String> monthNames = [
    'حمل',
    'ثور',
    'جوزا',
    'سرطان',
    'اسد',
    'سنبله',
    'میزان',
    'عقرب',
    'قوس',
    'جدی',
    'دلو',
    'حوت',
  ];

  // Persian day names
  static const List<String> dayNames = ['ش', 'ی', 'د', 'س', 'چ', 'پ', 'ج'];

  @override
  void initState() {
    super.initState();
    selectedDate = widget.initialDate;
    currentMonthIndex = _getMonthIndex(selectedDate);
    _pageController = PageController(initialPage: currentMonthIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  int _getMonthIndex(Jalali date) {
    return (date.year - widget.firstDate.year) * 12 +
        (date.month - widget.firstDate.month);
  }

  Jalali _getDateFromIndex(int index) {
    final totalMonths =
        widget.firstDate.month - 1 + index; // Convert to 0-based month
    final year = widget.firstDate.year + (totalMonths ~/ 12);
    final month = (totalMonths % 12) + 1; // Convert back to 1-based month
    return Jalali(year, month, 1);
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 350,
        height: 500,
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Header
            Text(
              widget.helpText,
              style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // Month/Year navigation
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                IconButton(
                  onPressed: () {
                    if (currentMonthIndex > 0) {
                      setState(() {
                        currentMonthIndex--;
                        _pageController.animateToPage(
                          currentMonthIndex,
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeInOut,
                        );
                      });
                    }
                  },
                  icon: const Icon(Icons.chevron_left),
                ),
                Text(
                  '${monthNames[_getDateFromIndex(currentMonthIndex).month - 1]} ${_getDateFromIndex(currentMonthIndex).year}',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  onPressed: () {
                    final maxIndex = _getMonthIndex(widget.lastDate);
                    if (currentMonthIndex < maxIndex) {
                      setState(() {
                        currentMonthIndex++;
                        _pageController.animateToPage(
                          currentMonthIndex,
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeInOut,
                        );
                      });
                    }
                  },
                  icon: const Icon(Icons.chevron_right),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Day names header
            Row(
              children: dayNames
                  .map(
                    (day) => Expanded(
                      child: Center(
                        child: Text(
                          day,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ),
                    ),
                  )
                  .toList(),
            ),
            const Divider(),

            // Calendar grid
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    currentMonthIndex = index;
                  });
                },
                itemCount: _getMonthIndex(widget.lastDate) + 1,
                itemBuilder: (context, index) {
                  return _buildCalendarGrid(_getDateFromIndex(index));
                },
              ),
            ),

            // Selected date display
            Container(
              padding: const EdgeInsets.all(12),
              margin: const EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'تاریخ انتخاب شده: ${selectedDate.day} ${monthNames[selectedDate.month - 1]} ${selectedDate.year}',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ),

            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: Text(widget.cancelText),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop(selectedDate.toDateTime());
                  },
                  child: Text(widget.confirmText),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCalendarGrid(Jalali monthDate) {
    final firstDayOfMonth = Jalali(monthDate.year, monthDate.month, 1);
    final firstWeekday = firstDayOfMonth.weekDay % 7; // 0 = Saturday

    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 7,
        childAspectRatio: 1,
      ),
      itemCount: 42, // 6 weeks * 7 days
      itemBuilder: (context, index) {
        final dayIndex = index - firstWeekday;

        if (dayIndex < 0 || dayIndex >= monthDate.monthLength) {
          return const SizedBox(); // Empty cell
        }

        final day = dayIndex + 1;
        final currentDate = Jalali(monthDate.year, monthDate.month, day);
        final isSelected = currentDate == selectedDate;
        final isToday = currentDate == Jalali.now();
        final isInRange =
            currentDate >= widget.firstDate && currentDate <= widget.lastDate;

        return GestureDetector(
          onTap: isInRange
              ? () {
                  setState(() {
                    selectedDate = currentDate;
                  });
                }
              : null,
          child: Container(
            margin: const EdgeInsets.all(2),
            decoration: BoxDecoration(
              color: isSelected
                  ? Colors.blue
                  : isToday
                  ? Colors.blue.shade100
                  : null,
              borderRadius: BorderRadius.circular(8),
              border: isToday && !isSelected
                  ? Border.all(color: Colors.blue, width: 2)
                  : null,
            ),
            child: Center(
              child: Text(
                day.toString(),
                style: TextStyle(
                  color: isSelected
                      ? Colors.white
                      : isInRange
                      ? Colors.black
                      : Colors.grey.shade400,
                  fontWeight: isSelected || isToday
                      ? FontWeight.bold
                      : FontWeight.normal,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

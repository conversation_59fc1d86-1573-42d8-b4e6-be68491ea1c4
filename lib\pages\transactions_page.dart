import 'package:flutter/material.dart';
import 'package:my_fincance_app/models/category.dart';
import 'package:my_fincance_app/models/loan.dart';
import 'package:my_fincance_app/models/transaction.dart';
import 'package:my_fincance_app/services/category_service.dart';
import 'package:my_fincance_app/services/loan_service.dart';
import 'package:my_fincance_app/services/transaction_service.dart';
import 'package:my_fincance_app/utils/currency_formatter.dart';
import 'package:my_fincance_app/utils/date_formatter.dart';
import 'package:my_fincance_app/utils/shamsi_date_picker.dart';
import 'package:provider/provider.dart';
import 'package:shamsi_date/shamsi_date.dart' as shamsi;

class TransactionsPage extends StatefulWidget {
  const TransactionsPage({super.key});

  @override
  State<TransactionsPage> createState() => _TransactionsPageState();
}

class _TransactionsPageState extends State<TransactionsPage> {
  String _selectedFilter = 'همه';
  String _selectedCategoryFilter = 'همه';
  DateTime? _startDate;
  DateTime? _endDate;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تراکنش‌ها'),
        centerTitle: true,
        backgroundColor: Colors.blue.shade50,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFilterDialog(context),
          ),
        ],
      ),
      backgroundColor: Colors.grey.shade50,
      body: Consumer3<TransactionService, CategoryService, LoanService>(
        builder:
            (context, transactionService, categoryService, loanService, child) {
              // Show loading indicator while data is being fetched
              if (transactionService.isLoading || categoryService.isLoading) {
                return const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(height: 16),
                      Text(
                        'در حال بارگذاری تراکنش‌ها...',
                        style: TextStyle(fontSize: 16, color: Colors.grey),
                      ),
                    ],
                  ),
                );
              }

              final filteredTransactions = _getFilteredTransactions(
                transactionService.transactions,
                categoryService,
              );

              if (transactionService.transactions.isEmpty) {
                return const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.receipt_long, size: 80, color: Colors.grey),
                      SizedBox(height: 16),
                      Text(
                        'هنوز تراکنشی ثبت نشده است',
                        style: TextStyle(fontSize: 18, color: Colors.grey),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'برای شروع، تراکنش جدید اضافه کنید',
                        style: TextStyle(fontSize: 14, color: Colors.grey),
                      ),
                    ],
                  ),
                );
              }

              if (filteredTransactions.isEmpty) {
                return const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.search_off, size: 80, color: Colors.grey),
                      SizedBox(height: 16),
                      Text(
                        'هیچ تراکنشی با فیلتر انتخابی یافت نشد',
                        style: TextStyle(fontSize: 18, color: Colors.grey),
                      ),
                    ],
                  ),
                );
              }

              return Column(
                children: [
                  // Filter summary
                  if (_hasActiveFilters()) _buildFilterSummary(),

                  // Transaction list
                  Expanded(
                    child: RefreshIndicator(
                      onRefresh: () async {
                        await transactionService.refresh();
                      },
                      child: ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: filteredTransactions.length,
                        itemBuilder: (context, index) {
                          final transaction = filteredTransactions[index];
                          return _buildTransactionCard(
                            transaction,
                            categoryService,
                            loanService,
                          );
                        },
                      ),
                    ),
                  ),
                ],
              );
            },
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          _showTransactionDialog(context);
        },
        icon: const Icon(Icons.add),
        label: const Text('افزودن تراکنش'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  List<Transaction> _getFilteredTransactions(
    List<Transaction> transactions,
    CategoryService categoryService,
  ) {
    return transactions.where((transaction) {
      // Filter by type
      if (_selectedFilter != 'همه' && transaction.type != _selectedFilter) {
        return false;
      }

      // Filter by category
      if (_selectedCategoryFilter != 'همه' &&
          transaction.categoryId != _selectedCategoryFilter) {
        return false;
      }

      // Filter by date range
      if (_startDate != null && transaction.date.isBefore(_startDate!)) {
        return false;
      }
      if (_endDate != null && transaction.date.isAfter(_endDate!)) {
        return false;
      }

      return true;
    }).toList()..sort((a, b) => b.date.compareTo(a.date));
  }

  bool _hasActiveFilters() {
    return _selectedFilter != 'همه' ||
        _selectedCategoryFilter != 'همه' ||
        _startDate != null ||
        _endDate != null;
  }

  Widget _buildFilterSummary() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Row(
        children: [
          const Icon(Icons.filter_list, color: Colors.blue),
          const SizedBox(width: 8),
          const Text(
            'فیلترهای فعال:',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              _getFilterSummaryText(),
              style: const TextStyle(fontSize: 12),
            ),
          ),
          TextButton(onPressed: _clearFilters, child: const Text('پاک کردن')),
        ],
      ),
    );
  }

  String _getFilterSummaryText() {
    final filters = <String>[];
    if (_selectedFilter != 'همه') filters.add(_selectedFilter);
    if (_selectedCategoryFilter != 'همه') filters.add('دسته انتخابی');
    if (_startDate != null || _endDate != null) filters.add('بازه زمانی');
    return filters.join(', ');
  }

  void _clearFilters() {
    setState(() {
      _selectedFilter = 'همه';
      _selectedCategoryFilter = 'همه';
      _startDate = null;
      _endDate = null;
    });
  }

  Widget _buildTransactionCard(
    Transaction transaction,
    CategoryService categoryService,
    LoanService loanService,
  ) {
    final category = categoryService.categories.firstWhere(
      (c) => c.id == transaction.categoryId,
      orElse: () => Category()..name = 'حذف شده',
    );

    final loan = transaction.loanId != null
        ? loanService.loans.firstWhere(
            (l) => l.id == transaction.loanId,
            orElse: () => Loan()..name = 'حذف شده',
          )
        : null;

    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header row
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: transaction.type == 'درآمد'
                      ? Colors.green.withValues(alpha: 0.1)
                      : Colors.red.withValues(alpha: 0.1),
                  child: Icon(
                    transaction.type == 'درآمد'
                        ? Icons.trending_up
                        : Icons.trending_down,
                    color: transaction.type == 'درآمد'
                        ? Colors.green
                        : Colors.red,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        transaction.description,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        category.name,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      CurrencyFormatter.formatWithPersianDigits(
                        transaction.amount,
                      ),
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: transaction.type == 'درآمد'
                            ? Colors.green
                            : Colors.red,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      DateFormatter.formatPersianDate(transaction.date),
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
                PopupMenuButton<String>(
                  onSelected: (value) {
                    if (value == 'edit') {
                      _showTransactionDialog(context, transaction: transaction);
                    } else if (value == 'delete') {
                      _showDeleteTransactionDialog(context, transaction);
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit, color: Colors.blue),
                          SizedBox(width: 8),
                          Text('ویرایش'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, color: Colors.red),
                          SizedBox(width: 8),
                          Text('حذف'),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),

            // Loan info if linked
            if (loan != null) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.link, size: 16, color: Colors.blue.shade700),
                    const SizedBox(width: 4),
                    Text(
                      'مربوط به: ${loan.name}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.blue.shade700,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Consumer<CategoryService>(
        builder: (context, categoryService, child) {
          return AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            title: const Row(
              children: [
                Icon(Icons.filter_list, color: Colors.blue),
                SizedBox(width: 8),
                Text('فیلتر تراکنش‌ها'),
              ],
            ),
            content: StatefulBuilder(
              builder: (context, setState) {
                return SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Type filter
                      DropdownButtonFormField<String>(
                        value: _selectedFilter,
                        decoration: const InputDecoration(
                          labelText: 'نوع تراکنش',
                          border: OutlineInputBorder(),
                        ),
                        items: ['همه', 'درآمد', 'مصرف'].map((String value) {
                          return DropdownMenuItem<String>(
                            value: value,
                            child: Text(value),
                          );
                        }).toList(),
                        onChanged: (newValue) {
                          setState(() {
                            _selectedFilter = newValue!;
                          });
                        },
                      ),
                      const SizedBox(height: 16),

                      // Category filter
                      DropdownButtonFormField<String>(
                        value: _selectedCategoryFilter,
                        decoration: const InputDecoration(
                          labelText: 'دسته‌بندی',
                          border: OutlineInputBorder(),
                        ),
                        items: [
                          const DropdownMenuItem(
                            value: 'همه',
                            child: Text('همه'),
                          ),
                          ...categoryService.categories.map((category) {
                            return DropdownMenuItem<String>(
                              value: category.id,
                              child: Text(category.name),
                            );
                          }),
                        ],
                        onChanged: (newValue) {
                          setState(() {
                            _selectedCategoryFilter = newValue!;
                          });
                        },
                      ),
                      const SizedBox(height: 16),

                      // Date range
                      Row(
                        children: [
                          Expanded(
                            child: OutlinedButton(
                              onPressed: () async {
                                final date =
                                    await ShamsiDatePicker.showShamsiDatePicker(
                                      context: context,
                                      initialDate: _startDate ?? DateTime.now(),
                                      firstDate: shamsi.Jalali(
                                        1400,
                                        1,
                                        1,
                                      ).toDateTime(),
                                      lastDate: shamsi.Jalali(
                                        1410,
                                        12,
                                        29,
                                      ).toDateTime(),
                                      helpText: 'انتخاب تاریخ شروع',
                                      confirmText: 'تأیید',
                                      cancelText: 'لغو',
                                    );
                                if (date != null) {
                                  setState(() {
                                    _startDate = date;
                                  });
                                }
                              },
                              child: Text(
                                _startDate != null
                                    ? DateFormatter.formatPersianDate(
                                        _startDate!,
                                      )
                                    : 'از تاریخ',
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: OutlinedButton(
                              onPressed: () async {
                                final date =
                                    await ShamsiDatePicker.showShamsiDatePicker(
                                      context: context,
                                      initialDate: _endDate ?? DateTime.now(),
                                      firstDate:
                                          _startDate ??
                                          shamsi.Jalali(
                                            1400,
                                            1,
                                            1,
                                          ).toDateTime(),
                                      lastDate: shamsi.Jalali(
                                        1410,
                                        12,
                                        29,
                                      ).toDateTime(),
                                      helpText: 'انتخاب تاریخ پایان',
                                      confirmText: 'تأیید',
                                      cancelText: 'لغو',
                                    );
                                if (date != null) {
                                  setState(() {
                                    _endDate = date;
                                  });
                                }
                              },
                              child: Text(
                                _endDate != null
                                    ? DateFormatter.formatPersianDate(_endDate!)
                                    : 'تا تاریخ',
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                );
              },
            ),
            actions: [
              TextButton(
                onPressed: () {
                  _clearFilters();
                  Navigator.of(context).pop();
                },
                child: const Text('پاک کردن'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('لغو'),
              ),
              ElevatedButton(
                onPressed: () {
                  setState(() {}); // Refresh the main page
                  Navigator.of(context).pop();
                },
                child: const Text('اعمال'),
              ),
            ],
          );
        },
      ),
    );
  }

  void _showTransactionDialog(
    BuildContext context, {
    Transaction? transaction,
  }) {
    final formKey = GlobalKey<FormState>();
    final descriptionController = TextEditingController(
      text: transaction?.description ?? '',
    );
    final amountController = TextEditingController(
      text: transaction?.amount.toString() ?? '',
    );
    String? selectedCategoryId = transaction?.categoryId;
    String? selectedLoanId = transaction?.loanId;
    String type = transaction?.type ?? 'مصرف';
    DateTime selectedDate = transaction?.date ?? DateTime.now();

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              title: Row(
                children: [
                  Icon(
                    transaction == null ? Icons.add : Icons.edit,
                    color: Colors.blue,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    transaction == null
                        ? 'افزودن تراکنش جدید'
                        : 'ویرایش تراکنش',
                  ),
                ],
              ),
              content: SingleChildScrollView(
                child: Form(
                  key: formKey,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Description field
                      TextFormField(
                        controller: descriptionController,
                        decoration: const InputDecoration(
                          labelText: 'توضیحات',
                          prefixIcon: Icon(Icons.description),
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'لطفاً توضیحات را وارد کنید';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Amount field
                      TextFormField(
                        controller: amountController,
                        decoration: const InputDecoration(
                          labelText: 'مبلغ (افغانی)',
                          prefixIcon: Icon(Icons.attach_money),
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'لطفاً مبلغ را وارد کنید';
                          }
                          if (double.tryParse(value) == null ||
                              double.parse(value) <= 0) {
                            return 'لطفاً مبلغ معتبر وارد کنید';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Type dropdown
                      DropdownButtonFormField<String>(
                        value: type,
                        decoration: const InputDecoration(
                          labelText: 'نوع تراکنش',
                          prefixIcon: Icon(Icons.category),
                          border: OutlineInputBorder(),
                        ),
                        items: [
                          const DropdownMenuItem(
                            value: 'درآمد',
                            child: Row(
                              children: [
                                Icon(Icons.trending_up, color: Colors.green),
                                SizedBox(width: 8),
                                Text('درآمد'),
                              ],
                            ),
                          ),
                          const DropdownMenuItem(
                            value: 'مصرف',
                            child: Row(
                              children: [
                                Icon(Icons.trending_down, color: Colors.red),
                                SizedBox(width: 8),
                                Text('مصرف'),
                              ],
                            ),
                          ),
                        ],
                        onChanged: (newValue) {
                          setState(() {
                            type = newValue!;
                            selectedCategoryId =
                                null; // Reset category when type changes
                          });
                        },
                      ),
                      const SizedBox(height: 16),

                      // Category dropdown
                      Consumer<CategoryService>(
                        builder: (context, categoryService, child) {
                          final filteredCategories = categoryService.categories
                              .where((c) => c.type == type)
                              .toList();

                          return DropdownButtonFormField<String>(
                            value: selectedCategoryId,
                            decoration: const InputDecoration(
                              labelText: 'دسته‌بندی',
                              prefixIcon: Icon(Icons.folder),
                              border: OutlineInputBorder(),
                            ),
                            hint: const Text('انتخاب دسته‌بندی'),
                            items: filteredCategories.map((Category category) {
                              return DropdownMenuItem<String>(
                                value: category.id,
                                child: Text(category.name),
                              );
                            }).toList(),
                            onChanged: (newValue) {
                              setState(() {
                                selectedCategoryId = newValue;
                              });
                            },
                            validator: (value) {
                              if (value == null) {
                                return 'لطفاً دسته‌بندی را انتخاب کنید';
                              }
                              return null;
                            },
                          );
                        },
                      ),
                      const SizedBox(height: 16),

                      // Loan dropdown (optional)
                      Consumer<LoanService>(
                        builder: (context, loanService, child) {
                          final activeLoans = loanService.activeLoans;

                          return DropdownButtonFormField<String>(
                            value: selectedLoanId,
                            decoration: const InputDecoration(
                              labelText: 'وام مربوطه (اختیاری)',
                              prefixIcon: Icon(Icons.link),
                              border: OutlineInputBorder(),
                            ),
                            hint: const Text('انتخاب وام (در صورت وجود)'),
                            items: [
                              const DropdownMenuItem<String>(
                                value: null,
                                child: Text('هیچکدام'),
                              ),
                              ...activeLoans.map((Loan loan) {
                                return DropdownMenuItem<String>(
                                  value: loan.id,
                                  child: Row(
                                    children: [
                                      Icon(
                                        loan.type == 'بدهی'
                                            ? Icons.money_off
                                            : Icons.account_balance_wallet,
                                        size: 16,
                                        color: loan.type == 'بدهی'
                                            ? Colors.red
                                            : Colors.green,
                                      ),
                                      const SizedBox(width: 8),
                                      Expanded(child: Text(loan.name)),
                                    ],
                                  ),
                                );
                              }),
                            ],
                            onChanged: (newValue) {
                              setState(() {
                                selectedLoanId = newValue;
                              });
                            },
                          );
                        },
                      ),
                      const SizedBox(height: 16),

                      // Date picker
                      Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: ListTile(
                          leading: const Icon(Icons.calendar_today),
                          title: const Text('تاریخ تراکنش'),
                          subtitle: Text(
                            DateFormatter.formatPersianDate(selectedDate),
                          ),
                          onTap: () async {
                            final date =
                                await ShamsiDatePicker.showShamsiDatePicker(
                                  context: context,
                                  initialDate: selectedDate,
                                  firstDate: shamsi.Jalali(
                                    1400,
                                    1,
                                    1,
                                  ).toDateTime(),
                                  lastDate: shamsi.Jalali(
                                    1410,
                                    12,
                                    29,
                                  ).toDateTime(),
                                  helpText: 'انتخاب تاریخ تراکنش',
                                  confirmText: 'تأیید',
                                  cancelText: 'لغو',
                                );
                            if (date != null) {
                              setState(() {
                                selectedDate = date;
                              });
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('لغو'),
                ),
                ElevatedButton(
                  onPressed: () async {
                    if (formKey.currentState!.validate()) {
                      try {
                        // Show loading indicator
                        showDialog(
                          context: context,
                          barrierDismissible: false,
                          builder: (context) =>
                              const Center(child: CircularProgressIndicator()),
                        );

                        if (transaction == null) {
                          await Provider.of<TransactionService>(
                            context,
                            listen: false,
                          ).addTransaction(
                            descriptionController.text.trim(),
                            double.parse(amountController.text),
                            type,
                            selectedCategoryId!,
                            loanId: selectedLoanId,
                            date: selectedDate,
                          );
                        } else {
                          await Provider.of<TransactionService>(
                            context,
                            listen: false,
                          ).updateTransaction(
                            transaction,
                            descriptionController.text.trim(),
                            double.parse(amountController.text),
                            type,
                            selectedCategoryId!,
                            loanId: selectedLoanId,
                            date: selectedDate,
                          );
                        }

                        // Close loading dialog
                        if (context.mounted) {
                          Navigator.of(context).pop();
                          // Close form dialog
                          Navigator.of(context).pop();

                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                transaction == null
                                    ? 'تراکنش جدید با موفقیت ثبت شد'
                                    : 'تراکنش با موفقیت ویرایش شد',
                              ),
                              backgroundColor: Colors.green,
                            ),
                          );
                        }
                      } catch (e) {
                        // Close loading dialog
                        if (context.mounted) {
                          Navigator.of(context).pop();

                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                'خطا در ثبت تراکنش: ${e.toString()}',
                              ),
                              backgroundColor: Colors.red,
                            ),
                          );
                        }
                      }
                    }
                  },
                  child: Text(transaction == null ? 'ثبت' : 'ویرایش'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _showDeleteTransactionDialog(
    BuildContext context,
    Transaction transaction,
  ) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: const Row(
            children: [
              Icon(Icons.warning, color: Colors.red),
              SizedBox(width: 8),
              Text('حذف تراکنش'),
            ],
          ),
          content: Text(
            'آیا مطمئن هستید که می‌خواهید تراکنش "${transaction.description}" را حذف کنید؟',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('لغو'),
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              onPressed: () async {
                try {
                  // Show loading indicator
                  showDialog(
                    context: context,
                    barrierDismissible: false,
                    builder: (context) =>
                        const Center(child: CircularProgressIndicator()),
                  );

                  await Provider.of<TransactionService>(
                    context,
                    listen: false,
                  ).deleteTransaction(transaction);

                  if (context.mounted) {
                    // Close loading dialog
                    Navigator.of(context).pop();
                    // Close delete dialog
                    Navigator.of(context).pop();

                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('تراکنش با موفقیت حذف شد'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                } catch (e) {
                  if (context.mounted) {
                    // Close loading dialog
                    Navigator.of(context).pop();

                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('خطا در حذف تراکنش: ${e.toString()}'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              },
              child: const Text('حذف', style: TextStyle(color: Colors.white)),
            ),
          ],
        );
      },
    );
  }
}
